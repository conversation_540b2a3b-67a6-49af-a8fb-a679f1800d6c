# 头像管理系统使用说明

## 概述

本系统提供了完整的头像管理功能，包括头像缓存、API集成、默认头像处理等。支持在聊天消息中显示发送者和接收者的头像，并提供了统一的头像组件。

## 核心文件

### 1. avatarService.js - 头像服务核心
位置：`web/src/utils/avatarService.js`

主要功能：
- 用户信息缓存（内存 + IndexedDB）
- API集成获取用户信息
- 头像URL处理和验证
- 默认头像处理

### 2. ChatAvatar组件 - 统一头像组件
位置：`web/src/components/ChatAvatar/index.vue`

特性：
- 自动处理头像URL
- 支持默认头像回退
- 可配置大小和点击事件
- 统一的样式和交互

### 3. 数据库扩展 - 用户信息缓存
位置：`web/src/utils/db.js`

新增功能：
- 用户信息表创建和管理
- 缓存过期处理（30分钟）
- 批量用户信息获取
- 自动清理过期缓存

## 主要API

### avatarService.js

```javascript
// 获取用户信息（包含头像）
const userInfo = await getUserInfo(userId)

// 获取当前用户头像信息
const currentUser = getCurrentUserAvatar()

// 处理头像URL
const processedUrl = processAvatarUrl(avatarUrl)

// 获取默认头像
const defaultAvatar = getDefaultAvatar()

// 批量获取用户信息
const usersMap = await getBatchUserInfo([userId1, userId2])
```

### 数据库操作

```javascript
// 保存用户信息到数据库
await saveUserInfo(userInfo)

// 从数据库获取用户信息
const userInfo = await getUserInfoFromDB(userId)

// 批量获取用户信息
const usersMap = await getBatchUserInfoFromDB(userIds)

// 清理过期缓存
const deletedCount = await clearExpiredUserInfo()
```

## 组件使用

### ChatAvatar组件

```vue
<template>
  <!-- 基础使用 -->
  <ChatAvatar :size="40" />
  
  <!-- 指定头像URL -->
  <ChatAvatar :size="60" src="https://example.com/avatar.jpg" />
  
  <!-- 使用用户信息对象 -->
  <ChatAvatar 
    :size="80" 
    :user-info="{ avatar: 'url', nickname: 'name' }"
  />
  
  <!-- 可点击头像 -->
  <ChatAvatar 
    :size="60" 
    :user-info="userInfo"
    :clickable="true"
    @click="handleAvatarClick"
  />
</template>
```

### MessageItem组件集成

MessageItem组件已自动集成ChatAvatar，会自动处理消息中的头像显示：

```javascript
// 消息对象结构
const message = {
  id: 'msg_id',
  content: '消息内容',
  avatar: '头像URL',           // 优先级较低
  senderAvatar: '发送者头像URL', // 优先级较高
  nickname: '用户昵称',
  isOwn: false
}
```

## 数据流程

### 1. 发送消息时
1. 获取当前用户头像信息 (`getCurrentUserAvatar()`)
2. 将头像信息包含在消息对象中
3. 保存到IndexedDB

### 2. 接收消息时（重要更新）
**群聊消息处理逻辑**：
1. 从WebSocket消息中获取发送者ID (`fromid`) 和群组ID (`groupID`)
2. 调用 `getUserInfoFromGroupMembers(fromid, groupID)` 获取发送者信息
3. 优先级顺序：
   - 内存缓存
   - IndexedDB缓存
   - **群成员信息查询**（新增）
   - API获取
4. 群成员信息查询流程：
   - 获取群组列表，找到对应群组
   - 调用 `getGroupMembers()` 获取群成员列表
   - 在群成员中查找匹配的用户ID
   - 提取用户的昵称和头像信息
5. 将完整的用户信息保存到消息对象和数据库

**私聊消息处理逻辑**：
1. 从WebSocket消息中获取发送者ID (`fromid`)
2. 调用 `getUserInfo(fromid)` 获取发送者信息
3. 优先从缓存获取，缓存未命中时从API获取

### 3. 显示消息时
1. MessageItem组件使用ChatAvatar显示头像
2. ChatAvatar自动处理头像URL和默认头像回退
3. 支持点击头像查看用户信息

## 缓存策略

### 三级缓存机制
1. **内存缓存**：Map对象，应用运行期间有效
2. **IndexedDB缓存**：持久化存储，30分钟过期
3. **API获取**：缓存未命中时从服务器获取

### 缓存清理
- 应用启动时自动清理过期缓存
- 支持手动清理过期缓存
- 内存缓存在应用重启时自动清空

## 配置说明

### 默认头像
默认头像路径：`/src/assets/My/noBody.png`

可通过修改 `avatarService.js` 中的 `DEFAULT_AVATAR` 常量来更改：

```javascript
const DEFAULT_AVATAR = '/src/assets/My/noBody.png'
```

### 缓存过期时间
默认30分钟，可在以下位置修改：

```javascript
// avatarService.js
const CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟

// db.js 中的相关函数也需要同步修改
```

### API集成
在 `avatarService.js` 的 `fetchUserFromAPI` 函数中配置实际的API调用：

```javascript
const fetchUserFromAPI = async (userId) => {
  try {
    // 替换为实际的API调用
    const response = await findImUser({ id: userId })
    // 处理响应数据...
  } catch (error) {
    // 错误处理...
  }
}
```

## 测试

测试页面位置：`web/src/view/chatManager/test/AvatarTest.vue`

包含以下测试场景：
- 默认头像显示
- 用户头像显示
- 可点击头像交互
- 用户信息获取
- 消息项头像显示

## 注意事项

1. **用户ID处理**：确保用户ID在整个系统中保持一致的类型（字符串或数字）
2. **头像URL验证**：系统会自动验证头像URL的有效性
3. **错误处理**：所有API调用都有完善的错误处理和回退机制
4. **性能优化**：使用了多级缓存和批量获取来优化性能
5. **数据一致性**：确保消息发送和接收时的头像数据一致性

## 扩展功能

### 自定义头像处理
可以扩展 `processAvatarUrl` 函数来支持更多的头像处理逻辑：

```javascript
export const processAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) return null
  
  // 添加自定义处理逻辑
  if (avatarUrl.startsWith('data:')) {
    // 处理base64图片
    return avatarUrl
  }
  
  if (avatarUrl.startsWith('/')) {
    // 处理相对路径
    return `${window.location.origin}${avatarUrl}`
  }
  
  // 其他处理逻辑...
  return avatarUrl
}
```

### 头像上传功能
可以扩展系统支持头像上传：

```javascript
export const uploadAvatar = async (file) => {
  // 实现头像上传逻辑
  // 返回上传后的头像URL
}
```

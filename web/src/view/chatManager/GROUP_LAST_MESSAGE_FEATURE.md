# 群聊消息列表显示最后一条消息功能

## 🎯 功能目标

在群聊消息列表中显示每个群聊的最后一条消息内容和时间，提升用户体验。

## 🔧 实现方案

### 1. 核心功能函数

#### `getGroupLastMessage(groupId)`
- **功能**: 从IndexedDB获取指定群聊的最后一条消息
- **返回**: 包含消息内容、时间和类型的对象
- **实现**: 使用 `getChatMessages(groupId, 1, 1)` 获取最新一条消息

```javascript
const getGroupLastMessage = async (groupId) => {
  try {
    const { getChatMessages } = await import('@/utils/db.js')
    const messages = await getChatMessages(groupId.toString(), 1, 1)
    
    if (messages && messages.length > 0) {
      const lastMessage = messages[0]
      return {
        content: lastMessage.msg || lastMessage.lastMessage || '',
        time: lastMessage.t || lastMessage.timestamp,
        type: lastMessage.typecode2 || 0
      }
    }
    return null
  } catch (error) {
    console.warn('获取群聊最后一条消息失败:', error)
    return null
  }
}
```

#### `formatMessageContent(content, type)`
- **功能**: 根据消息类型格式化显示内容
- **支持类型**:
  - 文本消息: 显示内容（限制30字符）
  - 语音: `[语音]`
  - 图片: `[图片]`
  - 视频: `[视频]`
  - 转发消息: `[转发消息]`
  - 撤回消息: `[消息已撤回]`
  - 语音通话: `[语音通话]`
  - 视频通话: `[视频通话]`

#### `getGroupUnreadCount(groupId)`
- **功能**: 获取群聊的未读消息数量
- **数据源**: WebSocket Store 或本地存储
- **返回**: 未读消息数量

### 2. 群聊列表增强

#### 修改 `fetchGroupList()` 函数
- 为每个群组并行获取最后一条消息和未读数量
- 使用 `Promise.all()` 提高性能
- 更新群聊对象结构

```javascript
// 为每个群组获取最后一条消息和未读数量
const groupsWithLastMessage = await Promise.all(
  responseData.data.map(async (group) => {
    const [lastMessageInfo, unreadCount] = await Promise.all([
      getGroupLastMessage(group.ID),
      getGroupUnreadCount(group.ID)
    ])
    
    return {
      id: `group_${group.ID}`,
      type: 'group',
      name: group.GroupName,
      avatar: group.GroupHeader || '',
      lastMessage: lastMessageInfo ? 
        formatMessageContent(lastMessageInfo.content, lastMessageInfo.type) : 
        '暂无消息',
      lastTime: lastMessageInfo ? 
        formatTime(lastMessageInfo.time) : 
        formatTime(group.CreatedAt),
      unread: unreadCount,
      online: true,
      originalData: group
    }
  })
)
```

### 3. 实时更新机制

#### `updateGroupLastMessage(groupId, messageData)`
- **功能**: 实时更新群聊列表中的最后一条消息
- **触发时机**: 收到新的群聊消息时
- **更新内容**: 最后消息、时间、未读数量
- **排序**: 将有新消息的群聊移到列表顶部

#### WebSocket消息监听
```javascript
// 监听WebSocket消息更新群聊列表
watch(() => webSocketStore.lastMessage, (newMessage) => {
  if (newMessage && newMessage.typecode === 2) { // 群聊消息
    const groupId = newMessage.toid || newMessage.groupID
    if (groupId) {
      updateGroupLastMessage(groupId, newMessage)
    }
  }
}, { deep: true })
```

## 📊 数据流程

### 初始加载流程
```
1. 调用 fetchGroupList()
2. 获取群组基本信息 (API)
3. 并行获取每个群聊的最后消息 (IndexedDB)
4. 并行获取每个群聊的未读数量 (Store)
5. 格式化消息内容显示
6. 渲染群聊列表
```

### 实时更新流程
```
1. WebSocket 接收新消息
2. 判断是否为群聊消息 (typecode === 2)
3. 提取群组ID
4. 调用 updateGroupLastMessage()
5. 更新对应群聊的最后消息和时间
6. 更新未读数量（如果不是当前群聊）
7. 将群聊移到列表顶部
8. 重新渲染列表
```

## 🎨 用户界面效果

### 群聊列表显示
- **群聊名称**: 显示群组名称
- **最后消息**: 根据类型显示格式化内容
- **时间**: 显示最后消息时间
- **未读标识**: 红色数字显示未读数量
- **排序**: 按最后消息时间倒序排列

### 消息类型显示示例
```
文本消息: "大家好，今天开会..."
语音消息: "[语音]"
图片消息: "[图片]"
视频消息: "[视频]"
撤回消息: "[消息已撤回]"
通话消息: "[语音通话]"
```

## 🚀 性能优化

### 1. 并行处理
- 使用 `Promise.all()` 并行获取多个群聊的最后消息
- 避免串行处理导致的性能问题

### 2. 缓存机制
- 利用IndexedDB缓存消息数据
- 减少重复的数据库查询

### 3. 增量更新
- 只更新有变化的群聊项
- 避免全量重新渲染

## 🔍 错误处理

### 1. 数据库查询失败
- 捕获异常并返回默认值
- 显示 "暂无消息" 而不是错误信息

### 2. 消息格式异常
- 安全的字符串处理
- 防止undefined/null导致的显示问题

### 3. 网络异常
- API调用失败时的降级处理
- 保持现有数据不变

## 📱 兼容性

- ✅ 支持所有现代浏览器
- ✅ 兼容现有的WebSocket消息处理
- ✅ 不影响现有的聊天功能
- ✅ 向后兼容旧的消息数据格式

## 🔧 后续优化建议

1. **消息预览优化**: 支持更多消息类型的预览
2. **性能监控**: 添加加载时间统计
3. **离线支持**: 改进离线状态下的数据处理
4. **搜索功能**: 支持按最后消息内容搜索群聊
5. **置顶功能**: 支持群聊置顶显示

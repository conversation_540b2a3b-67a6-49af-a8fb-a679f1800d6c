<template>
  <div class="message-item" :class="{ 'is-own': isOwn }">
    <div class="message-wrapper">
      <!-- 他人消息：头像在左，消息在右 -->
      <template v-if="!isOwn">
        <div class="avatar-section">
          <ChatAvatar
            :size="36"
            :user-info="message"
            :clickable="true"
            custom-class="message-avatar"
            @click="handleAvatarClick"
          />
        </div>

        <div class="message-content">
          <div class="message-meta">
            <span class="nickname">{{ message.nickname }}</span>
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>

          <div class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'image'" class="image-content">
                <el-image
                  :src="message.content"
                  fit="cover"
                  class="message-image"
                  :preview-src-list="[message.content]"
                />
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 自己消息：头像在右，消息在左 -->
      <template v-else>
        <div class="avatar-section">
          <ChatAvatar
            :size="36"
            :user-info="message"
            custom-class="message-avatar"
          />
        </div>

        <div class="message-content own-content">
          <div class="message-meta own-meta">
            <span class="time">{{ formatTime(message.createdAt) }}</span>
          </div>

          <div class="message-bubble" :class="getBubbleClass()">
            <div class="bubble-content">
              <div v-if="message.type === 'text'" class="text-content">
                {{ message.content }}
              </div>

              <div v-else-if="message.type === 'image'" class="image-content">
                <el-image
                  :src="message.content"
                  fit="cover"
                  class="message-image"
                  :preview-src-list="[message.content]"
                />
              </div>

              <div v-else-if="message.type === 'file'" class="file-content">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ message.fileName || '文件' }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { Document } from '@element-plus/icons-vue'
import ChatAvatar from '@/components/ChatAvatar/index.vue'

defineOptions({
  name: 'MessageItem'
})

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isOwn: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['avatar-click'])

// 格式化时间
const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取气泡样式类
const getBubbleClass = () => {
  return {
    'own-bubble': props.isOwn,
    'other-bubble': !props.isOwn,
    [`${props.message.type}-bubble`]: true
  }
}

// 头像点击事件
const handleAvatarClick = () => {
  emit('avatar-click', props.message)
}
</script>

<style lang="scss" scoped>
.message-item {
  margin-bottom: 16px;
}

.message-wrapper {
  display: flex;
  gap: 10px;
  max-width: 100%;
  justify-content: flex-start; // 别人的消息默认左对齐
}

// 自己的消息：整体在右边显示
.message-item.is-own {
  .message-wrapper {
    flex-direction: row;
    justify-content: flex-end; // 整体右对齐
  }
}

.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;

  .message-avatar {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 250px;
  min-width: 0;

  &.own-content {
    align-items: flex-start; // 自己的消息左对齐
  }
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;

  .nickname {
    color: #f3f4f6;
    font-weight: 500;
  }

  .time {
    color: #9ca3af;
  }

  &.own-meta {
    justify-content: flex-start; // 自己的消息时间左对齐
    margin-bottom: 4px;
    margin-top: 0;
  }
}

.message-bubble {
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  border-radius: 12px;
  padding: 10px 14px;

  &.own-bubble {
    background: #22c55e;
    color: white;

    &::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 12px;
      width: 0;
      height: 0;
      border-right: 6px solid #22c55e;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  &.other-bubble {
    background: #4b5563;
    color: #f3f4f6;
    border: 1px solid #6b7280;

    &::after {
      content: '';
      position: absolute;
      right: -6px;
      top: 12px;
      width: 0;
      height: 0;
      border-left: 6px solid #4b5563;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  .bubble-content {
    .text-content {
      line-height: 1.4;
    }

    .image-content {
      .message-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        cursor: pointer;
      }
    }

    .file-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px;

      .file-icon {
        font-size: 20px;
        color: #60a5fa;
      }

      .file-name {
        font-size: 14px;
      }
    }
  }

  .message-status {
    margin-top: 4px;
    text-align: right;
  }
}
</style>

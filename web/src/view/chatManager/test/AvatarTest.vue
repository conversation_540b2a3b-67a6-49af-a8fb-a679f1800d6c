<template>
  <div class="avatar-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>头像功能测试</span>
        </div>
      </template>

      <div class="test-section">
        <h3>1. 默认头像测试</h3>
        <div class="avatar-row">
          <ChatAvatar :size="40" />
          <ChatAvatar :size="60" />
          <ChatAvatar :size="80" />
        </div>
      </div>

      <div class="test-section">
        <h3>2. 用户头像测试</h3>
        <div class="avatar-row">
          <ChatAvatar 
            :size="40" 
            :user-info="{ avatar: 'https://picsum.photos/100/100?random=1' }"
          />
          <ChatAvatar 
            :size="60" 
            :user-info="{ senderAvatar: 'https://picsum.photos/100/100?random=2' }"
          />
          <ChatAvatar 
            :size="80" 
            src="https://picsum.photos/100/100?random=3"
          />
        </div>
      </div>

      <div class="test-section">
        <h3>3. 可点击头像测试</h3>
        <div class="avatar-row">
          <ChatAvatar 
            :size="60" 
            :user-info="{ 
              id: '10001',
              nickname: '测试用户1',
              avatar: 'https://picsum.photos/100/100?random=4' 
            }"
            :clickable="true"
            @click="handleAvatarClick"
          />
          <ChatAvatar 
            :size="60" 
            :user-info="{ 
              id: '10002',
              nickname: '测试用户2',
              senderAvatar: 'https://picsum.photos/100/100?random=5' 
            }"
            :clickable="true"
            @click="handleAvatarClick"
          />
        </div>
      </div>

      <div class="test-section">
        <h3>4. 用户信息获取测试</h3>
        <div class="user-info-test">
          <el-input 
            v-model="testUserId" 
            placeholder="输入用户ID"
            style="width: 200px; margin-right: 10px;"
          />
          <el-button @click="fetchUserInfo" :loading="loading">获取用户信息</el-button>
        </div>
        
        <div v-if="userInfo" class="user-info-result">
          <div class="user-card">
            <ChatAvatar 
              :size="80" 
              :user-info="userInfo"
            />
            <div class="user-details">
              <p><strong>ID:</strong> {{ userInfo.id }}</p>
              <p><strong>昵称:</strong> {{ userInfo.nickname }}</p>
              <p><strong>头像URL:</strong> {{ userInfo.avatar }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>5. 消息项头像测试</h3>
        <div class="message-test">
          <MessageItem 
            v-for="message in testMessages"
            :key="message.id"
            :message="message"
            :is-own="message.isOwn"
            @avatar-click="handleAvatarClick"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ChatAvatar from '@/components/ChatAvatar/index.vue'
import MessageItem from '../components/MessageItem.vue'
import { getUserInfo } from '@/utils/avatarService.js'

const testUserId = ref('10003')
const userInfo = ref(null)
const loading = ref(false)

// 测试消息数据
const testMessages = ref([
  {
    id: '1',
    content: '这是一条测试消息',
    type: 'text',
    createdAt: new Date(),
    isOwn: false,
    avatar: 'https://picsum.photos/100/100?random=6',
    senderAvatar: 'https://picsum.photos/100/100?random=6',
    nickname: '测试用户A'
  },
  {
    id: '2',
    content: '这是我发送的消息',
    type: 'text',
    createdAt: new Date(),
    isOwn: true,
    avatar: 'https://picsum.photos/100/100?random=7',
    senderAvatar: 'https://picsum.photos/100/100?random=7',
    nickname: '我'
  },
  {
    id: '3',
    content: '没有头像的消息',
    type: 'text',
    createdAt: new Date(),
    isOwn: false,
    avatar: '',
    senderAvatar: '',
    nickname: '无头像用户'
  }
])

// 处理头像点击
const handleAvatarClick = (userInfo) => {
  ElMessage.success(`点击了用户: ${userInfo.nickname || userInfo.id}`)
  console.log('点击的用户信息:', userInfo)
}

// 获取用户信息
const fetchUserInfo = async () => {
  if (!testUserId.value) {
    ElMessage.warning('请输入用户ID')
    return
  }

  loading.value = true
  try {
    userInfo.value = await getUserInfo(testUserId.value)
    ElMessage.success('获取用户信息成功')
  } catch (error) {
    ElMessage.error('获取用户信息失败: ' + error.message)
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.avatar-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
  
  h3 {
    margin-bottom: 15px;
    color: #409eff;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
  }
}

.avatar-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.user-info-test {
  margin-bottom: 15px;
}

.user-info-result {
  margin-top: 15px;
}

.user-card {
  display: flex;
  gap: 15px;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
}

.user-details {
  p {
    margin: 5px 0;
    font-size: 14px;
    
    strong {
      color: #606266;
    }
  }
}

.message-test {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  background-color: #fafafa;
  max-height: 300px;
  overflow-y: auto;
}
</style>
